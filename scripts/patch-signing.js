const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");

// Load .env file
dotenv.config({ path: path.join(__dirname, "..", ".env") });

// Validate required variables
const required = [
  "KEYSTORE_PATH",
  "KEYSTORE_PASSWORD",
  "KEY_ALIAS",
  "KEY_PASSWORD",
];
for (const key of required) {
  if (!process.env[key]) {
    console.error(`❌ Missing ${key} in .env`);
    process.exit(1);
  }
}

// Resolve absolute path for keystore
const keystorePath = process.env.KEYSTORE_PATH.startsWith(".")
  ? path.resolve(__dirname, "..", process.env.KEYSTORE_PATH)
  : process.env.KEYSTORE_PATH;

// Read and patch build.gradle
const gradleFile = path.join(__dirname, "..", "android", "app", "build.gradle");
let content = fs.readFileSync(gradleFile, "utf8");

// Replace debug signing with release
content = content.replace(
  /release\s*{\s*signingConfig signingConfigs.debug/,
  `release {
        signingConfig signingConfigs.release`,
);

// Inject signingConfigs if not already there
if (!content.includes("signingConfigs { release {")) {
  content = content.replace(
    /signingConfigs\s*{\s*debug\s*{[^}]+}/,
    `signingConfigs {
        release {
            storeFile file("${keystorePath.replace(/\\/g, "/")}")
            storePassword "${process.env.KEYSTORE_PASSWORD}"
            keyAlias "${process.env.KEY_ALIAS}"
            keyPassword "${process.env.KEY_PASSWORD}"
        }
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }`,
  );
}

content = content.replace(
  /signingConfig signingConfigs\.debug/g,
  "signingConfig signingConfigs.release",
);

fs.writeFileSync(gradleFile, content);
console.log("✅ build.gradle patched for release signing using .env values.");
