{"name": "healthnet.homecare.digitaltools-mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "format": "prettier --write .", "lint-check": "expo lint", "format-check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@expo-google-fonts/nunito": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@hnhdtools/delivery-api": "^0.11.0", "@hnhdtools/ui-kit": "0.9.3", "@hnhdtools/user-api": "0.10.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.77.2", "expo": "~53.0.9", "expo-auth-session": "~6.1.5", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.8", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-linking": "~7.1.5", "expo-router": "~5.0.6", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.59.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "styled-components": "^6.1.18", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "prettier": "^3.5.3", "react-native-svg-transformer": "^1.5.1", "typescript": "~5.8.3"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"enabled": true, "exclude": ["@hnhdtools/user-api", "styled-components"], "listUnknownPackages": true}}}}