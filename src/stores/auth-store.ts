import { createStore } from "zustand";
import { createJSONStorage, devtools, persist } from "zustand/middleware";
import * as SecureStore from "expo-secure-store";

const TOKEN_KEY = "accessToken";

type AuthState = {
  token: string | null;
  hasHydrated: boolean;
  setToken: (token: string | null) => void;
  clearToken: () => void;
  hydrate: () => void;
};

export const authStore = createStore<AuthState>()(
  devtools(
    persist(
      (set) => ({
        code: null,
        token: null,
        postcode: null,
        hasHydrated: false,
        setToken: (token) => set({ token }),
        clearToken: () => set({ token: null }),
        hydrate: () => set({ hasHydrated: true }),
      }),
      {
        name: TOKEN_KEY,

        storage: createJSONStorage(() => ({
          getItem: (name) => SecureStore.getItemAsync(name),
          removeItem: (name) => SecureStore.deleteItemAsync(name),
          setItem: (name, value) => SecureStore.setItemAsync(name, value),
        })),

        onRehydrateStorage: () => {
          return (state) => {
            state?.hydrate();
          };
        },
      },
    ),
  ),
);
