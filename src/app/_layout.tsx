import { ErrorBoundaryProps, Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import "react-native-reanimated";
import { Suspense, useEffect } from "react";
import * as Linking from "expo-linking";
import { ThemeProvider as StyledThemeProvider } from "styled-components/native";
import { ServicesProvider } from "@/components/providers/ServiceProvider";
import { UserManagementProvider } from "@/components/providers/UserManagementProvider";
import { ActivityIndicator, View, SafeAreaView } from "react-native";
import { AuthProvider } from "@/components/providers/AuthProvider";
import { defaultTheme } from "@hnhdtools/ui-kit/native";

import { ErrorBoundary as DefaultErrorBoundary } from "@/components/ui/ErrorBoundary";
import { useNunitoFont } from "@/hooks/useNunitoFont";
import { DeliveryManagementProvider } from "@/components/providers/DeliveryManagementProvider";

export function ErrorBoundary({ error, retry }: ErrorBoundaryProps) {
  return (
    <StyledThemeProvider theme={defaultTheme}>
      <DefaultErrorBoundary error={error} retry={retry} />
    </StyledThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded] = useNunitoFont();

  useEffect(() => {
    const sub = Linking.addEventListener("url", (event) => {
      console.log("🔗 Deep link received:", event.url);
    });

    return () => sub.remove();
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <AuthProvider>
      <ServicesProvider>
        <StyledThemeProvider theme={defaultTheme}>
          <UserManagementProvider>
            <DeliveryManagementProvider>
              <Suspense
                fallback={
                  <View
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <ActivityIndicator size="large" />
                  </View>
                }
              >
                <SafeAreaView style={{ flex: 1, backgroundColor: "red" }}>
                  <Stack>
                    <Stack.Screen
                      name="index"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen
                      name="(tabs)"
                      options={{ headerShown: false }}
                    />
                    <Stack.Screen name="+not-found" />
                  </Stack>
                </SafeAreaView>
              </Suspense>
              <StatusBar style="auto" />
            </DeliveryManagementProvider>
          </UserManagementProvider>
        </StyledThemeProvider>
      </ServicesProvider>
    </AuthProvider>
  );
}
