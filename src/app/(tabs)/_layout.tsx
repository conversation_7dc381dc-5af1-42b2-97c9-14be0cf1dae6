import { <PERSON>, Tabs, usePathname } from "expo-router";
import React from "react";
import { Platform } from "react-native";

import { HapticTab } from "@/components/HapticTab";
import TabBarBackground from "@/components/ui/TabBarBackground";
import HomeIcon from "@/components/ui/icons/home-02.svg";
import TruckIcon from "@/components/ui/icons/truck-02.svg";
import HeartIcon from "@/components/ui/icons/heart.svg";
import CalendarIcon from "@/components/ui/icons/calendar.svg";
import UserIcon from "@/components/ui/icons/user-circle.svg";
import { useTheme } from "styled-components/native";

export { ErrorBoundary } from "@/components/ui/ErrorBoundary";

const ICON_SIZE = 21;

export default function TabLayout() {
  const theme = useTheme();
  const pathname = usePathname();

  const isTopLevel = pathname.split("/").length === 2;

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.palette.primary.navy(4),
        tabBarInactiveTintColor: theme.palette.base.white(),
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: "absolute",
          },
          default: {},
        }),
        tabBarActiveBackgroundColor: theme.palette.primary.navy(10),
        tabBarInactiveBackgroundColor: theme.palette.primary.navy(10),
        headerShadowVisible: false,
        headerShown: isTopLevel,
        headerRight: () => (
          <Link href="/home/<USER>">
            <UserIcon width={31} height={31} stroke="#0D3F69" />
          </Link>
        ),
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: "Home",
          headerTitle: "",
          tabBarIcon: ({ color }) => (
            <HomeIcon width={ICON_SIZE} height={ICON_SIZE} stroke={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="deliveries"
        options={{
          title: "Deliveries",
          tabBarIcon: ({ color }) => (
            <TruckIcon width={ICON_SIZE} height={ICON_SIZE} stroke={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="nursing"
        options={{
          title: "Nursing",
          tabBarIcon: ({ color }) => (
            <HeartIcon width={ICON_SIZE} height={ICON_SIZE} stroke={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="calendar"
        options={{
          title: "Calendar",
          tabBarIcon: ({ color }) => (
            <CalendarIcon width={ICON_SIZE} height={ICON_SIZE} stroke={color} />
          ),
        }}
      />
    </Tabs>
  );
}
