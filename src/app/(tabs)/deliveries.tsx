import {
  useDeliveryListSuspense,
  useDeliveryPatientRemindersStatusSuspense,
} from "@hnhdtools/delivery-api";
import { View, Text } from "react-native";
import { PatientReminderBanner } from "@/components/pages/home/<USER>";

export default function DeliveriesPage() {
  //  const { data: deliveries } = useDeliveryListSuspense({});
  const { data: patientRemindersStatus } =
    useDeliveryPatientRemindersStatusSuspense();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Text>Deliveries page</Text>
      {/*<Text>{JSON.stringify(deliveries, null, " ")}</Text>*/}
      <PatientReminderBanner patientRemindersStatus={patientRemindersStatus} />
    </View>
  );
}
