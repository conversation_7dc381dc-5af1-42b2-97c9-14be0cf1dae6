import { useDeliveryListSuspense } from "@hnhdtools/delivery-api";
import { View, Text } from "react-native";

export default function DeliveriesPage() {
  // const { data: deliveries } = useDeliveryListSuspense({});

  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <Text>Deliveries page</Text>
      {/*<Text>{JSON.stringify(deliveries, null, " ")}</Text>*/}
    </View>
  );
}
