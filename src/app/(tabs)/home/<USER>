import { UserDetailSection } from "@/components/pages/manage-account/user-details";
import { UiHeading, UiText } from "@/components/ui/Text";
import { ScrollView, TouchableOpacity, View } from "react-native";
import { styled } from "styled-components/native";
import CalendarIcon from "@/components/ui/icons/calendar.svg";
import { useUserGetCurrentUserSuspense } from "@hnhdtools/user-api";
import { Href, useRouter } from "expo-router";

const Container = styled(View)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.palette.base.white(),
  gap: theme.spacing("6xl"),
}));

const DetailsContainer = styled(View)(({ theme }) => ({
  padding: theme.spacing("xl"),
  gap: theme.spacing("xl"),
  position: "relative",
}));

const EllipseContainer = styled(View)(() => ({
  bottom: 0,
  position: "relative",
  alignItems: "center",
}));

const Ellipse = styled(View)(({ theme }) => ({
  width: theme.spacing(120),
  height: theme.spacing(120),
  borderRadius: theme.spacing(120),
  backgroundColor: theme.palette.primary.navy(1),
  position: "absolute",
  top: -theme.spacing(105),
  zIndex: 0,
}));

const Title = styled(UiHeading).attrs(() => ({
  $size: "sm",
  $weight: 700,
}))(({ theme }) => ({
  color: theme.palette.primary.navy(8),
}));

const Content = styled(View)(() => ({}));

const ContentTitle = styled(UiText).attrs(() => ({
  $size: "lg",
  $weight: 600,
}))(({ theme }) => ({
  color: theme.palette.primary.grey(11),
  padding: theme.spacing("xl"),
}));

const ContentSection = styled(View)(({ theme }) => ({
  gap: theme.spacing("lg"),
}));

const ContentSectionItem = styled(View)(({ theme }) => ({
  padding: theme.spacing("xl"),
  flexDirection: "row",
  justifyContent: "space-between",
}));

const Separator = styled(View)(({ theme }) => ({
  borderBottomWidth: 1,
  borderStyle: "solid",
  borderColor: theme.palette.primary.grey(4),
}));

export default function ManageAccountPage() {
  const { data: user } = useUserGetCurrentUserSuspense();

  return (
    <ScrollView>
      <Container>
        <DetailsContainer>
          <EllipseContainer>
            <Ellipse />
          </EllipseContainer>
          <Title>My account</Title>
          <UserDetailSection
            name={user.fullName ?? ""}
            ctNumber={user.userName ?? ""}
            email={user.email ?? ""}
          />
        </DetailsContainer>
        <Content>
          <ContentSection>
            <ContentTitle>Documents</ContentTitle>
            <ContentSectionItem>
              <UiText>Welcome pack</UiText>
              <CalendarIcon width={20} height={20} stroke="black" />
            </ContentSectionItem>
          </ContentSection>
          <ContentSection>
            <ContentTitle>Manage account</ContentTitle>
            <View>
              <SectionItem
                href="/home/<USER>"
                title="Contact details"
              />
              <Separator />
              <SectionItem
                href="/home/<USER>"
                title="Log in and security"
              />
              <Separator />
              <SectionItem
                href="/home/<USER>"
                title="Data use and consent"
              />
              <Separator />
              <SectionItem
                href="/home/<USER>"
                title="Accessibility/ Text size"
              />
            </View>
          </ContentSection>
        </Content>
      </Container>
    </ScrollView>
  );
}

function SectionItem({ href, title }: { href: Href; title: string }) {
  const router = useRouter();

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={() => router.push(href)}>
      <ContentSectionItem>
        <UiText>{title}</UiText>
        <CalendarIcon width={20} height={20} stroke="black" />
      </ContentSectionItem>
    </TouchableOpacity>
  );
}
