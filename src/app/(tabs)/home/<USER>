import { authStore } from "@/stores/auth-store";
import { useUserGetCurrentUserSuspense } from "@hnhdtools/user-api";
import { useRouter } from "expo-router";
import React from "react";
import { Text, View } from "react-native";
import { useStore } from "zustand";
import { ButtonText, Button } from "@hnhdtools/ui-kit/native";
import { useDeliveryPatientRemindersStatusSuspense } from "@hnhdtools/delivery-api";

const Heading = styled.h2`
  color: ${({ theme }) => theme.palette.primary.navy(10)};
  font-size: ${({ theme }) => theme.text.fontSize("xl")};
  font-weight: 700;
`;

export default function HomeScreen() {
  const { token } = useStore(authStore);

  const { data: patientRemindersStatus } =
    useDeliveryPatientRemindersStatusSuspense();

  const router = useRouter();

  if (!token) {
    return (
      <View
        style={{
          flex: 1,
          padding: 5,
          backgroundColor: "#fff",
        }}
      >
        <Text style={{ color: "black" }}>No token</Text>
      </View>
    );
  }

  return (
    <View
      style={{
        flex: 1,
        padding: 5,
        backgroundColor: "#fff",
      }}
    >
      <UserTitle />

      <Heading>
        <Trans>What`s next</Trans>
      </Heading>
    </View>
  );
}

function UserTitle() {
  const { data: currentUser } = useUserGetCurrentUserSuspense();
  return <Text>Welcome patient, {currentUser.fullName}</Text>;
}
