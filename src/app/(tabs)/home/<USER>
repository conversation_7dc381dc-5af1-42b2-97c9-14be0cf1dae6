import { Stack } from "expo-router";
import { useTheme } from "styled-components/native";
import { View } from "react-native";

export default function HomeLayout() {
  const theme = useTheme();

  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="manage-account"
        options={{
          headerBackground: () => (
            <View
              style={{
                backgroundColor: theme.palette.primary.navy(1),
                height: "100%",
              }}
            />
          ),
          title: "",
        }}
      />
      <Stack.Screen
        name="contact-details"
        options={{
          headerBackground: () => (
            <View
              style={{
                backgroundColor: theme.palette.base.white(),
                height: "100%",
              }}
            />
          ),
          title: "",
        }}
      />
    </Stack>
  );
}
