import { authStore } from "@/stores/auth-store";
import { useUserGetCurrentUserSuspense } from "@hnhdtools/user-api";
import React from "react";
import { View } from "react-native";
import { useStore } from "zustand";
import { styled } from "styled-components/native";
import { useDeliveryPatientRemindersStatusSuspense } from "@hnhdtools/delivery-api";
import { UiText, UiHeading } from "@/components/ui/Text";
import { PatientReminderBanner } from "@/components/pages/home/<USER>";

const Container = styled(View)(({ theme }) => ({
  flex: 1,
  padding: theme.spacing("lg"),
  backgroundColor: theme.palette.base.white(),
}));

const Heading = styled(UiHeading).attrs(() => ({
  $size: "sm",
  $weight: 700,
}))(({ theme }) => ({
  color: theme.palette.primary.navy(10),
  marginBottom: theme.spacing("lg"),
}));

const WelcomeText = styled(UiText).attrs(() => ({
  $size: "lg",
  $weight: 500,
}))(({ theme }) => ({
  color: theme.palette.primary.grey(9),
  marginBottom: theme.spacing("xl"),
}));

const NoTokenContainer = styled(View)(({ theme }) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  padding: theme.spacing("lg"),
  backgroundColor: theme.palette.base.white(),
}));

const NoTokenText = styled(UiText).attrs(() => ({
  $size: "md",
  $weight: 500,
}))(({ theme }) => ({
  color: theme.palette.primary.error(600),
}));

export default function HomeScreen() {
  const { token } = useStore(authStore);

  const { data: patientRemindersStatus } =
    useDeliveryPatientRemindersStatusSuspense();

  if (!token) {
    return (
      <NoTokenContainer>
        <NoTokenText>No token available</NoTokenText>
      </NoTokenContainer>
    );
  }

  return (
    <Container>
      <UserTitle />

      <Heading>What&#39;s next</Heading>

      <PatientReminderBanner patientRemindersStatus={patientRemindersStatus} />
    </Container>
  );
}

function UserTitle() {
  const { data: currentUser } = useUserGetCurrentUserSuspense();
  return <WelcomeText>Welcome patient, {currentUser.fullName}</WelcomeText>;
}
