import { authStore } from "@/stores/auth-store";
import { useUserGetCurrentUserSuspense } from "@hnhdtools/user-api";
import { useRouter } from "expo-router";
import React from "react";
import { Text, View } from "react-native";
import { useStore } from "zustand";
import { ButtonText, Button } from "@hnhdtools/ui-kit/native";

export default function HomeScreen() {
  const { token } = useStore(authStore);

  const router = useRouter();

  if (!token) {
    return (
      <View
        style={{
          flex: 1,
          padding: 5,
          backgroundColor: "#fff",
        }}
      >
        <Text style={{ color: "black" }}>No token</Text>
      </View>
    );
  }

  return (
    <View
      style={{
        flex: 1,
        padding: 5,
        backgroundColor: "#fff",
      }}
    >
      <UserTitle />

      <Button
        onPress={() => {
          router.navigate("/logout");
        }}
      >
        <ButtonText>Logout</ButtonText>
      </Button>
    </View>
  );
}

function UserTitle() {
  const { data: currentUser } = useUserGetCurrentUserSuspense();
  return <Text>Welcome patient, {currentUser.fullName}</Text>;
}
