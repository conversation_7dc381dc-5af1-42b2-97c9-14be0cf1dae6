import { UiHeading, UiText } from "@/components/ui/Text";
import { ReactNode, useEffect, useState } from "react";
import {
  ScrollView,
  View,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from "react-native";
import { styled, useTheme } from "styled-components/native";
import Icon from "@/components/ui/icons/user-circle.svg";
import { Link, useRouter } from "expo-router";
import { Button, ButtonText } from "@hnhdtools/ui-kit/native";
import {
  useUserGetUserProfileSuspense,
  useUserUpdateUserProfile,
} from "@hnhdtools/user-api";
import { useForm, Controller } from "react-hook-form";
import { TextField } from "@/components/ui/Input";

const Container = styled(View)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.palette.base.white(),
}));

const Title = styled(UiHeading).attrs(() => ({
  $size: "sm",
  $weight: 700,
}))(({ theme }) => ({
  color: theme.palette.primary.navy(8),
  paddingInline: theme.spacing("xl"),
  paddingBlock: theme.spacing("lg"),
}));

const Content = styled(View)(({ theme }) => ({
  padding: theme.spacing("xl"),
  gap: theme.spacing("3xl"),
}));

type FormValue = {
  phoneNo: string;
  mobilePhoneNo: string;
  address: string;
  address2: string;
  city: string;
  coutry: string;
  postCode: string;
};

export default function ContactDetailsPage() {
  const { data: user } = useUserGetUserProfileSuspense();
  const { mutateAsync, isPending } = useUserUpdateUserProfile();
  const router = useRouter();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValue>({
    defaultValues: {
      phoneNo: user.userProfile?.phoneNo ?? "",
      mobilePhoneNo: user.userProfile?.mobilePhoneNo ?? "",
      address: user.userProfile?.address ?? "",
      address2: user.userProfile?.address2 ?? "",
      city: user.userProfile?.city ?? "",
      coutry: user.userProfile?.county ?? "",
      postCode: user.userProfile?.postCode ?? "",
    },
  });

  const onSubmit = async (data: FormValue) => {
    try {
      await mutateAsync({ ...user.userProfile, ...data });

      console.log("can go back", router.canGoBack());
      if (router.canGoBack()) {
        console.log("can go back");
        router.back();
      }
    } catch (error) {
      console.log("catch error", error);
    }
  };

  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const subopen = Keyboard.addListener("keyboardDidShow", () => {
      setVisible(true);
    });

    const subclose = Keyboard.addListener("keyboardDidHide", () => {
      setVisible(false);
    });

    return () => {
      subopen.remove();
      subclose.remove();
    };
  }, []);

  return (
    <Container>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          style={{ flex: 1 }}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ paddingBottom: visible ? 100 : 0 }}
        >
          <Title>Contact details</Title>

          <Content>
            <Info />

            <Controller
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextField
                  title="Home phone number"
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  error={errors.phoneNo ? "Field is required" : null}
                />
              )}
              name="phoneNo"
            />

            <Controller
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextField
                  title="Mobile phone number"
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  error={errors.mobilePhoneNo ? "Field is required" : null}
                />
              )}
              name="mobilePhoneNo"
            />

            <Controller
              control={control}
              // rules={{ required: true }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextField
                  title="Address line 1"
                  required={true}
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  error={errors.address ? "Field is required" : null}
                />
              )}
              name="address"
            />

            <Controller
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextField
                  title="Address line 2"
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  error={errors.address2 ? "Field is required" : null}
                />
              )}
              name="address2"
            />

            <Controller
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextField
                  title="City"
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  error={errors.city ? "Field is required" : null}
                />
              )}
              name="city"
            />

            <Controller
              control={control}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextField
                  title="Country"
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  error={errors.coutry ? "Field is required" : null}
                />
              )}
              name="coutry"
            />

            <Controller
              control={control}
              // rules={{ required: true }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextField
                  title="Post code"
                  required={true}
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  error={errors.postCode ? "Field is required" : null}
                />
              )}
              name="postCode"
            />

            <Button onPress={handleSubmit(onSubmit)} disabled={isPending}>
              <ButtonText>Update</ButtonText>
            </Button>
          </Content>
        </ScrollView>
      </KeyboardAvoidingView>
    </Container>
  );
}

const InfoMessageContainer = styled(View)(({ theme }) => ({
  flexDirection: "row",
  padding: theme.spacing("xl"),
  gap: theme.spacing("xl"),
  borderRadius: theme.spacing("xl"),
  borderWidth: 1,
  borderStyle: "solid",
  borderColor: theme.palette.primary.navy(6),
  backgroundColor: theme.palette.primary.navy(1),
}));

const InfoMesssageTitle = styled(UiText).attrs(() => ({
  $size: "sm",
  $weight: 600,
}))(({ theme }) => ({
  color: theme.palette.primary.grey(10),
}));

const InfoMesssageSubtitle = styled(UiText).attrs(() => ({
  $size: "sm",
  $weight: 400,
}))(({ theme }) => ({
  color: theme.palette.primary.grey(10),
}));

const InfoMessageTextContent = styled(View)(({ theme }) => ({
  gap: theme.spacing("xs"),
  flex: 1,
}));

function InfoMessage({
  icon,
  title,
  subtitle,
}: {
  title: ReactNode;
  subtitle: ReactNode;
  icon: ReactNode;
}) {
  return (
    <InfoMessageContainer>
      {icon}
      <InfoMessageTextContent>
        {title}
        {subtitle}
      </InfoMessageTextContent>
    </InfoMessageContainer>
  );
}

const InlineLink = styled(Link)(({ theme }) => ({
  color: theme.palette.primary.navy(6),
  textDecorationLine: "underline",
}));

function Info() {
  const theme = useTheme();

  return (
    <InfoMessage
      icon={
        <Icon width={20} height={20} stroke={theme.palette.primary.navy(7)} />
      }
      title={
        <InfoMesssageTitle>
          Updates to this page will not change the delivery address for active
          orders
        </InfoMesssageTitle>
      }
      subtitle={
        <InfoMesssageSubtitle>
          Go to the <InlineLink href="/home">orders page</InlineLink> to edit
          the delivery address for active orders. Your referring hospital will
          not be informed of the change.
        </InfoMesssageSubtitle>
      }
    />
  );
}
