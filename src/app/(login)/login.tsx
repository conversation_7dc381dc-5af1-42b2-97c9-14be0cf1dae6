import { UiText } from "@/components/ui/Text";
import { useAuth } from "@/hooks/useAuth";
import { authStore } from "@/stores/auth-store";
import { useRouter } from "expo-router";
import { useEffect } from "react";
import { Platform, View } from "react-native";
import { useStore } from "zustand";

export default function LoginScreen() {
  const { hasHydrated, token } = useStore(authStore);
  const { promptAsync, request } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!hasHydrated) {
      return;
    }

    if (token) {
      router.navigate("/home");
      return;
    }

    if (!request) {
      return;
    }

    if (!request.url) {
      return;
    }

    promptAsync({
      readerMode: false,
    })
      .then((result) => {
        if (Platform.OS !== "ios") {
          return;
        }

        if (result.type !== "success") {
          return;
        }

        router.push({
          pathname: "/signin-callback",
          params: result.params,
        });
      })
      .catch((error) => {
        console.log("promptAsync error", error);
      });
  }, [hasHydrated, token, router, promptAsync, request]);

  return (
    <View>
      <UiText>Waiting token to open home page</UiText>
    </View>
  );
}
