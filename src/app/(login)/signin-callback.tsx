import { AuthSessionProvider } from "@/components/providers/AuthSessionProvider";
import { useAuth } from "@/hooks/useAuth";
import { authStore } from "@/stores/auth-store";
import { useEffect } from "react";
import { useStore } from "zustand";

export default function SigninCallbackScreen() {
  const { discovery, response, request, getToken } = useAuth();
  const { setToken } = useStore(authStore);

  useEffect(() => {
    let mounted = true;

    if (!discovery) {
      return;
    }

    if (response?.type === "success") {
      const { code } = response.params;

      getToken(discovery, code, request?.codeVerifier).then((result) => {
        if (mounted) {
          setToken(result.accessToken);
        }
      });
    }

    return () => {
      mounted = false;
    };
  }, [discovery, getToken, request?.codeVerifier, response, setToken]);

  return <AuthSessionProvider />;
}
