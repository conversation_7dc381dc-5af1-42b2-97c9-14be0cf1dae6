import { FC } from 'react';
import { styled } from 'styled-components/native';
import { View, Text } from 'react-native';
import { UiText } from '@/components/ui/Text';

const PrescriptionStatusContainer = styled(View)<{
  $isPending: boolean;
}>(({ theme, $isPending }) => ({
  flexDirection: 'column',
  alignItems: 'flex-start',
  paddingVertical: theme.spacing('xs'),
  paddingHorizontal: theme.spacing('lg'),
  gap: theme.spacing('lg'),
  backgroundColor: $isPending
    ? theme.palette.primary.navy(1)
    : theme.palette.primary.success(50),
  borderRadius: theme.spacing('md'),
  borderWidth: 1,
  borderColor: $isPending
    ? theme.palette.primary.navy(3)
    : theme.palette.primary.success(200),
  borderStyle: 'solid',
}));

const PrescriptionStatusIndicator = styled(View)<{
  $isPending: boolean;
}>(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  padding: 0,
  gap: theme.spacing('sm'),
}));

const StatusDot = styled(View)<{ $isPending: boolean }>(({ theme, $isPending }) => ({
  width: 8,
  height: 8,
  borderRadius: 4,
  backgroundColor: $isPending
    ? theme.palette.primary.navy(6)
    : theme.palette.primary.success(500),
}));

const PrescriptionStatusText = styled(UiText).attrs(() => ({
  $size: 'sm' as const,
  $weight: 500 as const,
}))<{ $isPending: boolean }>(({ theme, $isPending }) => ({
  color: $isPending
    ? theme.palette.primary.navy(7)
    : theme.palette.primary.success(700),
}));

interface PrescriptionStatusProps {
  isPending: boolean;
}

export const PrescriptionStatus: FC<PrescriptionStatusProps> = ({
  isPending,
}) => (
  <PrescriptionStatusContainer $isPending={isPending}>
    <PrescriptionStatusIndicator $isPending={isPending}>
      <StatusDot $isPending={isPending} />
      <PrescriptionStatusText $isPending={isPending}>
        {isPending ? (
          <Text>Prescription pending</Text>
        ) : (
          <Text>Active prescription</Text>
        )}
      </PrescriptionStatusText>
    </PrescriptionStatusIndicator>
  </PrescriptionStatusContainer>
);
