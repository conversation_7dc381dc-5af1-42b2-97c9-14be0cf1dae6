import type { GetPatientRemindersStatusResponse } from "@hnhdtools/delivery-api";
import { FC, ReactNode } from "react";
import { Text } from "react-native";
import { Alert, AlertType } from "@/components/ui/Alert";

type BannerType = "bloodTest" | "prescriptionPending" | "activePrescription";

interface BannerConfig {
  title: ReactNode;
  description: ReactNode;
  type: AlertType;
}

const BANNER_CONFIG: Record<BannerType, BannerConfig> = {
  bloodTest: {
    type: "info",
    title: <Text>Ensure your blood tests are done</Text>,
    description: (
      <Text>
        Otherwise this can slow down the processing of your prescription.
        We&#39;ll let you know by email or SMS when it&#39;s time to confirm
        your next order.
      </Text>
    ),
  },
  prescriptionPending: {
    type: "info",
    title: (
      <Text>
        We&#39;ll let you know when it&#39;s time to confirm your order
      </Text>
    ),
    description: (
      <Text>
        Your prescription is currently being processed by your hospital team.
        We&#39;ll let you know by email or SMS when it&#39;s time to confirm
        your next order.
      </Text>
    ),
  },
  activePrescription: {
    type: "base",
    title: (
      <Text>
        We&#39;ll let you know when it&#39;s time to confirm your next order
      </Text>
    ),
    description: (
      <Text>
        You have an active prescription. We&#39;ll let you know by email or SMS
        once it&#39;s time to confirm your next order.
      </Text>
    ),
  },
};

function getBannerType(
  data: GetPatientRemindersStatusResponse,
): BannerType | null {
  if (!data) return null;

  const { bloodTestReminder } = data;

  const isPrescriptionPending = data.allCompletelyShipped;
  const isActivePrescription = !data.allCompletelyShipped;

  if (bloodTestReminder && isPrescriptionPending) {
    return "bloodTest";
  }

  if (isPrescriptionPending) {
    return "prescriptionPending";
  }

  if (isActivePrescription) {
    return "activePrescription";
  }

  return "bloodTest";
}

interface PatientReminderBannerProps {
  patientRemindersStatus: GetPatientRemindersStatusResponse;
  ignoreBloodTestReminder?: boolean;
}

export const PatientReminderBanner: FC<PatientReminderBannerProps> = ({
  patientRemindersStatus,
}) => {
  const bannerType: BannerType | null = getBannerType(
    patientRemindersStatus,
  );

  if (!bannerType) return null;
  const { title, description } = BANNER_CONFIG[bannerType];

  return (
    <Alert
      type="base"
      title={title}
      description={description}
      canClose={false}
    />
  );
};
