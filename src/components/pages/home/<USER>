"use client";

import type { GetPatientRemindersStatusResponse } from "@hnhdtools/delivery-api";
import { FC, ReactNode } from "react";
import { Alert, AlertType } from "@/components/ui/Alert";

type BannerType = "bloodTest" | "prescriptionPending" | "activePrescription";

interface BannerConfig {
  title: ReactNode;
  description: ReactNode;
  type: AlertType;
}

const BANNER_CONFIG: Record<BannerType, BannerConfig> = {
  bloodTest: {
    type: "info",
    title: <span>Ensure your blood tests are done</span>,
    description: (
      <span>
        Otherwise this can slow down the processing of your prescription. We’ll
        let you know by email or SMS when it’s time to confirm your next order.
      </span>
    ),
  },
  prescriptionPending: {
    type: "info",
    title: <span>We’ll let you know when it’s time to confirm your order</span>,
    description: (
      <span>
        Your prescription is currently being processed by your hospital team.
        We’ll let you know by email or SMS when it’s time to confirm your next
        order.
      </span>
    ),
  },
  activePrescription: {
    type: "base",
    title: (
      <span>We’ll let you know when it’s time to confirm your next order</span>
    ),
    description: (
      <span>
        You have an active prescription. We’ll let you know by email or SMS once
        it’s time to confirm your next order.
      </span>
    ),
  },
};

function getBannerType(
  data: GetPatientRemindersStatusResponse,
  ignoreBloodTestReminder?: boolean,
): BannerType | null {
  if (!data) return null;

  const { bloodTestReminder } = data;

  const isPrescriptionPending = data.allCompletelyShipped;
  const isActivePrescription = !data.allCompletelyShipped;

  if (bloodTestReminder && !ignoreBloodTestReminder) {
    return "bloodTest";
  }

  if (isPrescriptionPending) {
    return "prescriptionPending";
  }

  if (isActivePrescription) {
    return "activePrescription";
  }

  return "bloodTest";
}

interface PatientReminderBannerProps {
  patientRemindersStatus: GetPatientRemindersStatusResponse;
  ignoreBloodTestReminder?: boolean;
}

export const PatientReminderBanner: FC<PatientReminderBannerProps> = ({
  patientRemindersStatus,
  ignoreBloodTestReminder,
}) => {
  const bannerType: BannerType | null = getBannerType(
    patientRemindersStatus,
    ignoreBloodTestReminder,
  );

  if (!bannerType) return null;
  const { title, description } = BANNER_CONFIG[bannerType];

  return (
    <Alert
      type="base"
      title={title}
      description={description}
      canClose={false}
    />
  );
};
