import { UiText } from "@/components/ui/Text";
import { View } from "react-native";
import { styled } from "styled-components/native";

const Container = styled(View)(({ theme }) => ({
  gap: theme.spacing("lg"),
}));

const Title = styled(UiText).attrs(() => ({
  $size: "lg",
  $weight: 600,
}))(({ theme }) => ({
  color: theme.palette.primary.grey(11),
}));

const SubTitle = styled(UiText).attrs(() => ({
  $size: "sm",
  $weight: 500,
}))(({ theme }) => ({
  color: theme.palette.primary.grey(8),
}));

const Buttons = styled(View)(({ theme }) => ({
  flexDirection: "row",
  gap: theme.spacing("xl"),
}));

const InlineLink = styled(UiText).attrs(() => ({
  $size: "sm",
  $weight: 600,
}))(({ theme }) => ({
  color: theme.palette.primary.navy(5),
}));

export function UserDetailSection({
  name,
  ctNumber,
  email,
}: {
  name: string;
  ctNumber: string;
  email: string;
}) {
  return (
    <Container>
      <View>
        <Title>{name}</Title>
        <SubTitle>CT {ctNumber}</SubTitle>
        <SubTitle>Email {email}</SubTitle>
      </View>
      <Buttons>
        <InlineLink>Switch accounts</InlineLink>
        <InlineLink>Add account</InlineLink>
      </Buttons>
    </Container>
  );
}
