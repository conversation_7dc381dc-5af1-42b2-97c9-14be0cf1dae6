import { fontFamily } from "@/hooks/useNunitoFont";
import { Text as NativeText } from "react-native";
import { DefaultTheme, styled } from "styled-components/native";

type FontWeightKind = 400 | 500 | 600 | 700;

type TextFontSizeKind = Exclude<
  Parameters<DefaultTheme["text"]["fontSize"]>[0],
  number
>;

export const UiText = styled(NativeText)<{
  $size?: TextFontSizeKind;
  $weight?: FontWeightKind;
}>(({ theme, $size = "md", $weight = 500 }) => ({
  fontSize: theme.text.fontSize($size),
  lineHeight: theme.text.lineHeight($size),
  fontFamily: fontFamily($weight),
}));

type HeadingFontSizeKind = Exclude<
  Parameters<DefaultTheme["heading"]["fontSize"]>[0],
  number
>;

export const UiHeading = styled(NativeText)<{
  $size?: HeadingFontSizeKind;
  $weight?: FontWeightKind;
}>(({ theme, $size = "md", $weight = 500 }) => ({
  fontSize: theme.heading.fontSize($size),
  lineHeight: theme.heading.lineHeight($size),
  letterSpacing: theme.heading.letterSpacing($size),
  fontFamily: fontFamily($weight),
}));
