"use client";
import React, { useState, ReactNode } from "react";
import styled, { DefaultTheme } from "styled-components";

import AlertCircleIcon from "@/components/ui/icons/alert-circle.svg";
import CheckCircleIcon from "@/components/ui/icons/check.svg";
import CloseIcon from "@/components/ui/icons/x-close.svg";
import { palette, radius, spacing, text } from "@/helpers/theme";

export type AlertType = "base" | "info" | "success" | "warning" | "error";

type ColorMapItem = {
  icon: (theme: DefaultTheme) => string;
  bg: (theme: DefaultTheme) => string;
  border: (theme: DefaultTheme) => string;
};

const colorMap: Record<AlertType, ColorMapItem> = {
  base: {
    icon: (theme) => theme.palette.primary.navy(7),
    bg: (theme) => theme.palette.base.white(),
    border: (theme) => theme.palette.primary.grey(5),
  },
  info: {
    icon: (theme) => theme.palette.primary.navy(7),
    bg: (theme) => theme.palette.primary.navy(1),
    border: (theme) => theme.palette.primary.navy(6),
  },
  success: {
    icon: (theme) => theme.palette.primary.success(600),
    bg: (theme) => theme.palette.primary.success(50),
    border: (theme) => theme.palette.primary.success(300),
  },
  warning: {
    icon: (theme) => theme.palette.primary.warning(600),
    bg: (theme) => theme.palette.primary.warning(25),
    border: (theme) => theme.palette.primary.warning(300),
  },
  error: {
    icon: (theme) => theme.palette.primary.error(600),
    bg: (theme) => theme.palette.primary.error(50),
    border: (theme) => theme.palette.primary.error(300),
  },
};

const Wrapper = styled.div<{ type: AlertType; toast?: boolean }>`
  display: flex;
  align-items: flex-start;
  gap: ${spacing("xl")};
  padding: ${spacing("xl")};
  border-radius: ${radius("lg")};
  background-color: ${({ theme, type }) => colorMap[type].bg(theme)};
  border: 1px solid ${({ theme, type }) => colorMap[type].border(theme)};
  width: ${({ toast }) => (toast ? "auto" : "100%")};
  position: relative;
`;

const IconStyled = styled(AlertCircleIcon)<{ type: AlertType }>`
  width: 20px;
  height: 20px;

  path {
    stroke: ${({ theme, type }) => colorMap[type].icon(theme)} !important;
  }
`;

const CheckIconStyled = styled(CheckCircleIcon)`
  width: 20px;
  height: 20px;

  path:first-of-type {
    fill: ${({ theme }) => colorMap.success.icon(theme)} !important;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: ${spacing("lg")};
  right: ${spacing("md")};
  background: none;
  border: none;
  cursor: pointer;

  svg {
    width: 20px;
    height: 20px;

    path {
      stroke: ${palette.primary.grey(8)} !important;
    }
  }
`;

const Content = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: ${spacing("xl")};
  gap: ${spacing("xs")};

  &.without-close {
    margin-right: 0;
  }
`;

const Title = styled.div<{ type: AlertType }>`
  ${text("sm", "semibold")};
  color: ${palette.primary.grey(10)};
`;

// Description styling (unchanged)
const Description = styled.div`
  ${text("sm")};
  color: ${palette.primary.grey(8)};
`;

// Action button styling (unchanged, final)
const ActionButton = styled.button<{ type: AlertType }>`
  ${text("sm")};
  margin-top: ${spacing("sm")};
  background: none;
  border: none;
  cursor: pointer;
  color: ${({ theme, type }) => colorMap[type].icon(theme)};
  text-align: left;
  padding: 0;
`;

export interface AlertProps {
  id?: string;
  type?: AlertType;
  title?: ReactNode;
  description?: ReactNode;
  actionLabel?: string;
  onAction?: "dismiss" | (() => void);
  onClose?: () => void;
  canClose?: boolean;
  withoutIcon?: boolean;
  size?: "small" | "regular";
}

export const Alert: React.FC<AlertProps> = ({
  id,
  type = "info",
  title,
  description,
  actionLabel,
  onAction,
  onClose,
  canClose = true,
  withoutIcon = false,
}) => {
  const toast = Boolean(id);
  const [visible, setVisible] = useState(true);

  const handleClose = () => {
    setVisible(false);
    if (onClose) onClose();
  };

  if (!visible) return null;

  return (
    <Wrapper type={type} toast={toast} className="alert-wrapper">
      {withoutIcon ? null : (
        <>
          {type === "success" ? (
            <CheckIconStyled className="alert-icon" />
          ) : (
            <IconStyled type={type} className="alert-icon" />
          )}
        </>
      )}
      <Content className={`alert-content ${canClose ? "" : "without-close"}`}>
        {title && (
          <Title type={type} className="alert-title">
            {title}
          </Title>
        )}
        {description && (
          <Description className="alert-description">{description}</Description>
        )}
        {actionLabel && onAction && (
          <ActionButton
            type={type}
            onClick={onAction !== "dismiss" ? onAction : handleClose}
          >
            {actionLabel}
          </ActionButton>
        )}
      </Content>

      {canClose && (
        <CloseButton onClick={handleClose} aria-label="Close">
          <CloseIcon width={20} height={20} />
        </CloseButton>
      )}
    </Wrapper>
  );
};
