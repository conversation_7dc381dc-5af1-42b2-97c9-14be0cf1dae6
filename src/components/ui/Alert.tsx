import React, { useState, ReactNode } from "react";
import styled, { DefaultTheme } from "styled-components/native";
import { View, TouchableOpacity, Text } from "react-native";

import AlertCircleIcon from "@/components/ui/icons/alert-circle.svg";
import CheckCircleIcon from "@/components/ui/icons/check.svg";
import CloseIcon from "@/components/ui/icons/x-close.svg";

export type AlertType = "base" | "info" | "success" | "warning" | "error";

type ColorMapItem = {
  icon: (theme: DefaultTheme) => string;
  bg: (theme: DefaultTheme) => string;
  border: (theme: DefaultTheme) => string;
};

const colorMap: Record<AlertType, ColorMapItem> = {
  base: {
    icon: (theme) => theme.palette.primary.navy(7),
    bg: (theme) => theme.palette.base.white(),
    border: (theme) => theme.palette.primary.grey(5),
  },
  info: {
    icon: (theme) => theme.palette.primary.navy(7),
    bg: (theme) => theme.palette.primary.navy(1),
    border: (theme) => theme.palette.primary.navy(6),
  },
  success: {
    icon: (theme) => theme.palette.primary.success(600),
    bg: (theme) => theme.palette.primary.success(50),
    border: (theme) => theme.palette.primary.success(300),
  },
  warning: {
    icon: (theme) => theme.palette.primary.warning(600),
    bg: (theme) => theme.palette.primary.warning(25),
    border: (theme) => theme.palette.primary.warning(300),
  },
  error: {
    icon: (theme) => theme.palette.primary.error(600),
    bg: (theme) => theme.palette.primary.error(50),
    border: (theme) => theme.palette.primary.error(300),
  },
};

const Wrapper = styled(View)<{ type: AlertType; toast?: boolean }>(
  ({ theme, type, toast }) => ({
    flexDirection: "row",
    alignItems: "flex-start",
    gap: theme.spacing("xl"),
    padding: theme.spacing("xl"),
    borderRadius: theme.spacing("lg"),
    backgroundColor: colorMap[type].bg(theme),
    borderWidth: 1,
    borderColor: colorMap[type].border(theme),
    borderStyle: "solid",
    width: toast ? "auto" : "100%",
    position: "relative",
  }),
);

const IconStyled = styled(AlertCircleIcon)<{ type: AlertType }>(
  ({ theme, type }) => ({
    width: 20,
    height: 20,
    stroke: colorMap[type].icon(theme),
  }),
);

const CheckIconStyled = styled(CheckCircleIcon)(({ theme }) => ({
  width: 20,
  height: 20,
  fill: colorMap.success.icon(theme),
}));

const CloseButton = styled(TouchableOpacity)(({ theme }) => ({
  position: "absolute",
  top: theme.spacing("lg"),
  right: theme.spacing("md"),
}));

const CloseIconStyled = styled(CloseIcon)(({ theme }) => ({
  width: 20,
  height: 20,
  stroke: theme.palette.primary.grey(8),
}));

const Content = styled(View)<{ withoutClose?: boolean }>(
  ({ theme, withoutClose }) => ({
    flex: 1,
    flexDirection: "column",
    marginRight: withoutClose ? 0 : theme.spacing("xl"),
    gap: theme.spacing("xs"),
  }),
);

const Title = styled.Text<{ type: AlertType }>(({ theme, type }) => ({
  fontSize: theme.text.fontSize("sm"),
  lineHeight: theme.text.lineHeight("sm"),
  fontWeight: "600",
  color: theme.palette.primary.grey(10),
}));

const Description = styled.Text(({ theme }) => ({
  fontSize: theme.text.fontSize("sm"),
  lineHeight: theme.text.lineHeight("sm"),
  color: theme.palette.primary.grey(8),
}));

const ActionButton = styled(TouchableOpacity)<{ type: AlertType }>(
  ({ theme, type }) => ({
    marginTop: theme.spacing("sm"),
    padding: 0,
  }),
);

const ActionButtonText = styled.Text<{ type: AlertType }>(
  ({ theme, type }) => ({
    fontSize: theme.text.fontSize("sm"),
    lineHeight: theme.text.lineHeight("sm"),
    color: colorMap[type].icon(theme),
    textAlign: "left",
  }),
);

export interface AlertProps {
  id?: string;
  type?: AlertType;
  title?: ReactNode;
  description?: ReactNode;
  actionLabel?: string;
  onAction?: "dismiss" | (() => void);
  onClose?: () => void;
  canClose?: boolean;
  withoutIcon?: boolean;
  size?: "small" | "regular";
}

export const Alert: React.FC<AlertProps> = ({
  id,
  type = "info",
  title,
  description,
  actionLabel,
  onAction,
  onClose,
  canClose = true,
  withoutIcon = false,
}) => {
  const toast = Boolean(id);
  const [visible, setVisible] = useState(true);

  const handleClose = () => {
    setVisible(false);
    if (onClose) onClose();
  };

  if (!visible) return null;

  return (
    <Wrapper type={type} toast={toast}>
      {withoutIcon ? null : (
        <>
          {type === "success" ? (
            <CheckIconStyled />
          ) : (
            <IconStyled type={type} />
          )}
        </>
      )}
      <Content withoutClose={!canClose}>
        {title && <Title type={type}>{title}</Title>}
        {description && <Description>{description}</Description>}
        {actionLabel && onAction && (
          <ActionButton
            type={type}
            onPress={onAction !== "dismiss" ? onAction : handleClose}
          >
            <ActionButtonText type={type}>{actionLabel}</ActionButtonText>
          </ActionButton>
        )}
      </Content>

      {canClose && (
        <CloseButton onPress={handleClose}>
          <CloseIconStyled />
        </CloseButton>
      )}
    </Wrapper>
  );
};
