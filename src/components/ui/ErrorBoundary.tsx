import { ResponseError } from "@hnhdtools/user-api";
import { ErrorBoundaryProps } from "expo-router";
import { View } from "react-native";
import { styled } from "styled-components/native";
import { UiText } from "./Text";

const Container = styled(View)(({ theme }) => ({
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  padding: theme.spacing("xl"),
}));

export function ErrorBoundary({ error, retry }: ErrorBoundaryProps) {
  if (error instanceof ResponseError) {
    if (error.response.status === 401) {
      return null;
    }
  }

  return (
    <Container>
      <UiText style={{ textAlign: "center" }}>{error.message}</UiText>
      <UiText onPress={retry}>Try Again?</UiText>
    </Container>
  );
}
