import { styled } from "styled-components/native";
import { UiText } from "./Text";
import { TextInput, View } from "react-native";
import AlertCircleIcon from "@/components/ui/icons/alert-circle.svg";

const Title = styled(UiText).attrs(() => ({
  $size: "sm",
  $weight: 500,
}))(({ theme }) => ({
  color: theme.palette.primary.grey(9),
}));

const Required = styled(Title).attrs(() => ({
  children: "*",
}))(({ theme }) => ({
  color: theme.palette.primary.navy(7),
}));

const Container = styled(View)(({ theme }) => ({
  gap: theme.spacing("sm"),
}));

export function TextField({
  title,
  error,
  value,
  onChange,
  onBlur,
  required,
}: {
  title: string;
  error?: string | null;
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  required?: boolean;
}) {
  return (
    <Container>
      <Title>
        {title} {required ? <Required /> : null}
      </Title>

      <Input
        value={value}
        onChangeText={onChange}
        onBlur={onBlur}
        $hasError={Boolean(error)}
      />

      {error ? (
        <ErrorContainer>
          <ErrorIcon />

          <ErrorTitle>{error}</ErrorTitle>
        </ErrorContainer>
      ) : null}
    </Container>
  );
}

const Input = styled(TextInput)<{ $hasError?: boolean }>(
  ({ theme, $hasError }) => ({
    padding: theme.spacing("lg"),
    borderRadius: theme.spacing("md"),
    borderColor: $hasError
      ? theme.palette.primary.error(500)
      : theme.palette.primary.grey(5),
    borderStyle: "solid",
    borderWidth: 1,
    color: theme.palette.primary.grey(11),
    fontSize: theme.text.fontSize("md"),
    lineHeight: theme.text.lineHeight("md"),
    fontWeight: 400,
  }),
);

const ErrorContainer = styled(View)(({ theme }) => ({
  gap: theme.spacing("xs"),
  flexDirection: "row",
}));

const ErrorIcon = styled(AlertCircleIcon)(({ theme }) => ({
  width: theme.spacing("xl"),
  height: theme.spacing("xl"),
  stroke: theme.palette.primary.error(600),
}));

const ErrorTitle = styled(UiText).attrs(() => ({
  $size: "sm",
  $weight: 400,
}))(({ theme }) => ({
  color: theme.palette.primary.error(600),
}));
