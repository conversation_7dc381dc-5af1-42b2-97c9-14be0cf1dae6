import { ReactNode, useCallback, useMemo } from "react";
import { authContext, AuthSessionContextValue } from "@/contexts/auth";
import * as AuthSession from "expo-auth-session";
import { auth } from "@/constants/auth";
import { useQueryClient } from "@tanstack/react-query";
import { useStore } from "zustand";
import { authStore } from "@/stores/auth-store";

const signinRedirectUri = AuthSession.makeRedirectUri({
  native: auth.REDIRECT_URI,
});

type Props = {
  children?: ReactNode;
};

const config: AuthSession.AuthRequestConfig = {
  clientId: auth.CLIENT_ID,
  scopes: auth.SCOPE.split(" "),
  redirectUri: signinRedirectUri,
  usePKCE: true,
  extraParams: {
    prompt: "login", // Forces login even if browser has session
  },
};

const getToken = async (
  discovery: AuthSession.DiscoveryDocument,
  code: string,
  codeVerifier: string | undefined,
) => {
  const result = await AuthSession.exchangeCodeAsync(
    {
      clientId: auth.CLIENT_ID,
      code,
      redirectUri: signinRedirectUri,
      extraParams: {
        code_verifier: codeVerifier ?? "",
      },
    },
    discovery,
  );

  return result;
};

export function AuthSessionProvider(props: Props) {
  const { children } = props;

  const discovery = AuthSession.useAutoDiscovery(auth.AUTHORITY);
  const { token, setToken } = useStore(authStore);
  const queryClient = useQueryClient();

  const [request, response, promptAsync] = AuthSession.useAuthRequest(
    config,
    discovery,
  );

  const logout = useCallback(async () => {
    if (token && discovery) {
      await AuthSession.revokeAsync(
        {
          token,
          ...config,
          tokenTypeHint: AuthSession.TokenTypeHint.AccessToken,
        },
        discovery,
      );
    }

    setToken(null);

    queryClient.removeQueries();
  }, [discovery, queryClient, setToken, token]);

  const value = useMemo(() => {
    const value: AuthSessionContextValue = {
      discovery,
      response,
      request,
      promptAsync,
      logout,
      getToken,
    };

    return value;
  }, [discovery, request, promptAsync, logout, response]);

  return <authContext.Provider value={value}>{children}</authContext.Provider>;
}
