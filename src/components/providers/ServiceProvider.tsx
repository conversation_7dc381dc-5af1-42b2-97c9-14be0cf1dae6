import { ResponseError } from "@hnhdtools/user-api";
import {
  MutationCache,
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { router } from "expo-router";

const errorHandler = (message: string) => async (error: Error) => {
  if (error instanceof ResponseError) {
    if (error.response.status === 401) {
      router.push("/logout");
      console.log(message, "401");
      return;
    }

    const jsonBody = await error.response.json();

    console.log(
      message,
      error.response.status,
      JSON.stringify(jsonBody, null, " "),
    );

    return;
  }

  console.log(message, error);
};

const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: errorHandler("QUERY CLIENT ERROR"),
  }),
  mutationCache: new MutationCache({
    onError: errorHandler("MUTATION CLIENT ERROR"),
  }),
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      retry: false,
    },
  },
});

export function ServicesProvider(props: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      {props.children}
    </QueryClientProvider>
  );
}
