import { FC, ReactNode, useEffect } from "react";
import { useRouter } from "expo-router";
import { useStore } from "zustand";
import { authStore } from "@/stores/auth-store";

export const AuthProvider: FC<{ children?: ReactNode }> = ({ children }) => {
  const router = useRouter();
  const { hasHydrated, token } = useStore(authStore);

  useEffect(() => {
    if (!hasHydrated) {
      return;
    }

    if (token) {
      router.push("/home");
    } else {
      router.push("/login");
    }
  }, [hasHydrated, token, router]);

  return <>{children}</>;
};
