import { UserApiProvider } from "@hnhdtools/user-api";
import { FC, ReactNode } from "react";
import { useStore } from "zustand";
import { authStore } from "@/stores/auth-store";

export const UserManagementProvider: FC<{ children?: ReactNode }> = ({
  children,
}) => {
  const { token } = useStore(authStore);

  return (
    <UserApiProvider
      basePath="https://hhdt-dev-ms-ca-userman.yellowgrass-e607bd0a.uksouth.azurecontainerapps.io"
      token={token}
    >
      {children}
    </UserApiProvider>
  );
};
