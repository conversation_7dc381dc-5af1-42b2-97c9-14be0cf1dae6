import { DeliveryApiProvider } from "@hnhdtools/delivery-api";
import { FC, ReactNode } from "react";
import { useStore } from "zustand";
import { authStore } from "@/stores/auth-store";

const basePath =
  "https://hhdt-dev-ms-ca-deliveryman.yellowgrass-e607bd0a.uksouth.azurecontainerapps.io";

export const DeliveryManagementProvider: FC<{ children?: ReactNode }> = ({
  children,
}) => {
  const { token } = useStore(authStore);

  return (
    <DeliveryApiProvider basePath={basePath} token={token}>
      {children}
    </DeliveryApiProvider>
  );
};
