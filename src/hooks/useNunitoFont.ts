import { useFonts } from "@expo-google-fonts/nunito/useFonts";
import { Nunito_200ExtraLight } from "@expo-google-fonts/nunito/200ExtraLight";
import { Nunito_300Light } from "@expo-google-fonts/nunito/300Light";
import { Nunito_400Regular } from "@expo-google-fonts/nunito/400Regular";
import { Nunito_500Medium } from "@expo-google-fonts/nunito/500Medium";
import { Nunito_600SemiBold } from "@expo-google-fonts/nunito/600SemiBold";
import { Nunito_700Bold } from "@expo-google-fonts/nunito/700Bold";
import { Nunito_800ExtraBold } from "@expo-google-fonts/nunito/800ExtraBold";
import { Nunito_900Black } from "@expo-google-fonts/nunito/900Black";
import { Nunito_200ExtraLight_Italic } from "@expo-google-fonts/nunito/200ExtraLight_Italic";
import { Nunito_300Light_Italic } from "@expo-google-fonts/nunito/300Light_Italic";
import { Nunito_400Regular_Italic } from "@expo-google-fonts/nunito/400Regular_Italic";
import { Nunito_500Medium_Italic } from "@expo-google-fonts/nunito/500Medium_Italic";
import { Nunito_600SemiBold_Italic } from "@expo-google-fonts/nunito/600SemiBold_Italic";
import { Nunito_700Bold_Italic } from "@expo-google-fonts/nunito/700Bold_Italic";
import { Nunito_800ExtraBold_Italic } from "@expo-google-fonts/nunito/800ExtraBold_Italic";
import { Nunito_900Black_Italic } from "@expo-google-fonts/nunito/900Black_Italic";

const map = {
  200: "Nunito_200ExtraLight",
  300: "Nunito_300Light",
  400: "Nunito_400Regular",
  500: "Nunito_500Medium",

  600: "Nunito_600SemiBold",
  700: "Nunito_700Bold",
  800: "Nunito_800ExtraBold",
  900: "Nunito_900Black",

  ["200_italic"]: "Nunito_200ExtraLight_Italic",
  ["300_italic"]: "Nunito_300Light_Italic",
  ["400_italic"]: "Nunito_400Regular_Italic",
  ["500_italic"]: "Nunito_500Medium_Italic",

  ["600_italic"]: "Nunito_600SemiBold_Italic",
  ["700_italic"]: "Nunito_700Bold_Italic",
  ["800_italic"]: "Nunito_800ExtraBold_Italic",
  ["900_italic"]: "Nunito_900Black_Italic",
} as const;

export function fontFamily(
  weight: 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900,
  isItalic: boolean = false,
) {
  const key = isItalic ? (`${weight}_italic` as const) : weight;

  return map[key];
}

export function useNunitoFont() {
  return useFonts({
    Nunito_200ExtraLight,
    Nunito_300Light,
    Nunito_400Regular,
    Nunito_500Medium,

    Nunito_600SemiBold,
    Nunito_700Bold,
    Nunito_800ExtraBold,
    Nunito_900Black,

    Nunito_200ExtraLight_Italic,
    Nunito_300Light_Italic,
    Nunito_400Regular_Italic,
    Nunito_500Medium_Italic,

    Nunito_600SemiBold_Italic,
    Nunito_700Bold_Italic,
    Nunito_800ExtraBold_Italic,
    Nunito_900Black_Italic,
  });
}
