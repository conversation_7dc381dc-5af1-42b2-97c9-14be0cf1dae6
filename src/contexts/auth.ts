import * as AuthSession from "expo-auth-session";
import { createContext } from "react";

export type AuthSessionContextValue = {
  discovery: AuthSession.DiscoveryDocument | null;
  request: AuthSession.AuthRequest | null;
  response: AuthSession.AuthSessionResult | null;
  promptAsync: (
    options?: AuthSession.AuthRequestPromptOptions,
  ) => Promise<AuthSession.AuthSessionResult>;
  logout: () => void;
  getToken: (
    discovery: AuthSession.DiscoveryDocument,
    code: string,
    codeVerifier: string | undefined,
  ) => Promise<AuthSession.TokenResponse>;
};

export const authContext = createContext<AuthSessionContextValue | null>(null);
