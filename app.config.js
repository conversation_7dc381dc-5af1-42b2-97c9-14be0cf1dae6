const versionCode = parseInt(process.env.VERSION_CODE || "1", 10);

const packageName = process.env.PACKAGE_NAME ?? "com.healthnet.homecare.v2";

export default {
  expo: {
    name: "HealthNet.Homecare.DigitalTools-Mobile",
    slug: "hnhdtools-mobile-app",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "healthnethomecaredigitaltoolsmobile",
    userInterfaceStyle: "automatic",
    newArchEnabled: false,
    ios: {
      buildNumber: versionCode.toString(),
      supportsTablet: true,
      bundleIdentifier: packageName,
      teamId: "JBM9G5LUCC",
    },
    android: {
      versionCode,
      package: packageName,
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      windowSoftInputMode: "adjustResize",
      edgeToEdgeEnabled: true,
      signing: {
        keystore: process.env.KEYSTORE_PATH,
        keystorePassword: process.env.KEYSTORE_PASSWORD,
        keyAlias: process.env.KEY_ALIAS,
        keyPassword: process.env.KEY_PASSWORD,
      },
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png",
    },
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/splash-icon.png",
          imageWidth: 200,
          resizeMode: "contain",
          backgroundColor: "#ffffff",
        },
      ],
      "expo-secure-store",
      "expo-font",
    ],
    experiments: {
      typedRoutes: true,
    },
  },
};
