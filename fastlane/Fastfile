# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Push a new beta build to TestFlight"
  lane :beta do
    match(
      type: "appstore", # or "development" if just testing
      app_identifier: "com.healthnet.homecare.v2",
      git_url: ENV["MATCH_GIT_URL"], # store in .env or CI secrets
      readonly: false
    )
    # increment_build_number(xcodeproj: "HealthNetHomecareDigitalToolsMobile.xcodeproj")
    build_app(
      workspace: "ios/HealthNetHomecareDigitalToolsMobile.xcworkspace",
      scheme: "HealthNetHomecareDigitalToolsMobile",
      export_method: "app-store",
      export_options: {
        provisioningProfiles: {
          "com.healthnet.homecare.v2" => "match AppStore com.healthnet.homecare.v2"
        }
      }
    )
    upload_to_testflight
  end
end

platform :android do
  desc "Build and upload Android AAB to Google Play Internal Testing"
  lane :play do
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "./android"
    )

    upload_to_play_store(
      track: "internal",
      package_name: "com.healthnet.homecare.v2",
      aab: "android/app/build/outputs/bundle/release/app-release.aab",
      json_key: "fastlane/google-play-key.json",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end
end
