trigger:
  - develop

pool:
  vmImage: "ubuntu-latest"

variables:
  - group: "hhdt-mobile-android-app-dev"
  - name: "KeystoreFile"
    value: "HealthNet.keystore"
  - name: "googlePlayKeyJsonFile"
    value: "google-play-key.json"

steps:
  - script: |
      NODE_VERSION=$(grep nodejs .tool-versions | awk '{print $2}' | cut -d. -f1,2)
      echo "##vso[task.setvariable variable=NODE_VERSION]$NODE_VERSION"
    displayName: "Read Node.js version from .tool-versions"

  - task: NodeTool@0
    inputs:
      versionSpec: "$(NODE_VERSION)"
    displayName: "Use Node.js version $(NODE_VERSION) from .tool-versions"

  - script: |
      RUBY_VERSION=$(grep ruby .tool-versions | awk '{print $2}' | cut -d. -f1,2)
      echo "##vso[task.setvariable variable=RUBY_VERSION]$RUBY_VERSION"
    displayName: "Read Ruby version $(RUBY_VERSION) from .tool-versions"

  - task: UseRubyVersion@0
    inputs:
      versionSpec: "$(RUBY_VERSION)"
    displayName: "Use Ruby version from .tool-versions"

  - task: DownloadSecureFile@1
    name: downloadNpmrc
    inputs:
      secureFile: ".npmrc"

  - script: cp "$(downloadNpmrc.secureFilePath)" .npmrc
    displayName: "Move .npmrc"

  - script: |
      yarn install --frozen-lockfile
    displayName: "Install dependencies"

  - script: |
      git status
      git diff
    displayName: "Git status"

  - task: DownloadSecureFile@1
    name: downloadKeystore
    inputs:
      secureFile: "$(KeystoreFile)"
    displayName: "Download Android keystore"

  - script: cp "$(downloadKeystore.secureFilePath)" fastlane/HealthNet.keystore
    displayName: "Move keystore"

  - task: DownloadSecureFile@1
    name: downloadGooglePlayKey
    inputs:
      secureFile: "$(googlePlayKeyJsonFile)"
    displayName: "Download google-play-key.json"

  - script: cp "$(downloadGooglePlayKey.secureFilePath)" fastlane/google-play-key.json
    displayName: "Move google-play-key.json"

  - script: npx expo prebuild --platform android --no-install
    displayName: "Prebuild Expo (android)"
    env:
      VERSION_CODE: $(Build.BuildId)
      PACKAGE_NAME: $(PACKAGE_NAME)

  - script: node ./scripts/patch-signing
    displayName: "Patch signing"
    env:
      VERSION_CODE: $(Build.BuildId)
      PACKAGE_NAME: $(PACKAGE_NAME)
      KEY_ALIAS: $(KEY_ALIAS)
      KEYSTORE_PASSWORD: $(KEYSTORE_PASSWORD)
      KEYSTORE_PATH: $(KEYSTORE_PATH)
      KEY_PASSWORD: $(KEY_PASSWORD)

  - script: bundle install --jobs 4 --retry 3
    displayName: "Install Ruby Gems"

  - script: bundle exec fastlane android play
    displayName: "Build APK"
