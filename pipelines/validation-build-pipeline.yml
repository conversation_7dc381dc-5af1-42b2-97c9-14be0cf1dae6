# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger: none

pool:
  vmImage: ubuntu-latest

steps:
  - script: |
      NODE_VERSION=$(grep nodejs .tool-versions | awk '{print $2}')
      echo "##vso[task.setvariable variable=NODE_VERSION]$NODE_VERSION"
    displayName: "Read Node.js version from .tool-versions"

  - task: NodeTool@0
    inputs:
      versionSpec: "$(NODE_VERSION)"
    displayName: "Use Node.js version from .tool-versions"

  - task: DownloadSecureFile@1
    name: downloadNpmrc
    inputs:
      secureFile: ".npmrc"

  - script: |
      cp "$(downloadNpmrc.secureFilePath)" .npmrc
    displayName: "Move .npmrc"

  - script: yarn install --frozen-lockfile
    displayName: "Install dependencies"

  - script: yarn format-check
    displayName: "Format check"

  - script: yarn lint-check
    displayName: "Lint check"

  - script: yarn type-check
    displayName: "Type check"
