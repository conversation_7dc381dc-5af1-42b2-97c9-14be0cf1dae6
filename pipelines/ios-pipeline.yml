trigger: none

pool:
  vmImage: "macOS-15"

variables:
  - group: hhdt-mobile-ios-app-dev
  - name: SCHEME
    value: "HealthNetHomecareDigitalToolsMobile"
  - name: WORKSPACE
    value: "HealthNetHomecareDigitalToolsMobile.xcworkspace"
  - name: FASTLANE_LANE
    value: "ios beta"
  - name: BUNDLE_IDENTIFIER
    value: "com.healthnet.homecare.v2"

steps:
  - script: |
      NODE_VERSION=$(grep nodejs .tool-versions | awk '{print $2}')
      echo "##vso[task.setvariable variable=NODE_VERSION]$NODE_VERSION"
    displayName: "Read Node.js version from .tool-versions"

  # - task: Bash@3
  #   displayName: "Populate .env file"
  #   inputs:
  #     targetType: "inline"
  #     script: |
  #       echo "MATCH_PASSWORD=$MATCH_PASSWORD" >> .env
  #       echo "MATCH_GIT_URL=$MATCH_GIT_URL" >> .env
  #       echo "FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD=$FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD" >> .env
  #       echo "FASTLANE_PASSWORD=$FASTLANE_PASSWORD" >> .env
  #       echo "APP_STORE_CONNECT_API_KEY_KEY_ID=$APP_STORE_CONNECT_API_KEY_KEY_ID" >> .env
  #       echo "APP_STORE_CONNECT_API_KEY_ISSUER_ID=$APP_STORE_CONNECT_API_KEY_ISSUER_ID" >> .env
  #       echo "APP_STORE_CONNECT_API_KEY_KEY=$APP_STORE_CONNECT_API_KEY_KEY" >> .env
  #       echo "------ .env contents ------"
  #       cat .env
  #   env:
  #     MATCH_PASSWORD: $MATCH_PASSWORD
  #     MATCH_GIT_URL: $MATCH_GIT_URL
  #     FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: $FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD
  #     FASTLANE_PASSWORD: $FASTLANE_PASSWORD
  #     APP_STORE_CONNECT_API_KEY_KEY_ID: $APP_STORE_CONNECT_API_KEY_KEY_ID
  #     APP_STORE_CONNECT_API_KEY_ISSUER_ID: $APP_STORE_CONNECT_API_KEY_ISSUER_ID
  #     APP_STORE_CONNECT_API_KEY_KEY: $APP_STORE_CONNECT_API_KEY_KEY

  - task: NodeTool@0
    inputs:
      versionSpec: "$(NODE_VERSION)"
    displayName: "Use Node.js version from .tool-versions"

  - script: |
      RUBY_VERSION=$(grep ruby .tool-versions | awk '{print $2}')
      echo "##vso[task.setvariable variable=RUBY_VERSION]$RUBY_VERSION"
    displayName: "Read Ruby version from .tool-versions"

  - task: UseRubyVersion@0
    inputs:
      versionSpec: "$(RUBY_VERSION)"
    displayName: "Use Ruby version from .tool-versions"

  - task: DownloadSecureFile@1
    name: downloadNpmrc
    inputs:
      secureFile: ".npmrc"

  - script: |
      cp "$(downloadNpmrc.secureFilePath)" .npmrc
    displayName: "Move .npmrc"

  - script: |
      yarn install --frozen-lockfile
    displayName: "Install dependencies"

  - script: npx expo prebuild --platform ios --no-install
    displayName: "Prebuild Expo (ios)"

  - script: |
      git status
      git diff
    displayName: "Git status"

  - script: |
      gem install bundler
      bundle install
    displayName: "Install Fastlane dependencies"

  - task: InstallSSHKey@0
    inputs:
      sshKeySecureFile: match_ssh_key
      knownHostsEntry: "ssh.dev.azure.com"
    displayName: "Install SSH Key for Azure Repos access"

  - script: |
      mkdir -p ~/.ssh
      ssh-keyscan ssh.dev.azure.com >> ~/.ssh/known_hosts
    displayName: "Add Azure DevOps to known_hosts"

  - script: |
      git ls-remote "$MATCH_GIT_URL"
    displayName: "Check SSH Access to Match Repo"

  - script: |
      bundle exec fastlane $FASTLANE_LANE
    displayName: "Deploy to TestFlight"
    env:
      MATCH_PASSWORD: $MATCH_PASSWORD
      MATCH_GIT_URL: $MATCH_GIT_URL
      FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: $FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD
      FASTLANE_PASSWORD: $FASTLANE_PASSWORD
      APP_STORE_CONNECT_API_KEY_KEY_ID: $APP_STORE_CONNECT_API_KEY_KEY_ID
      APP_STORE_CONNECT_API_KEY_ISSUER_ID: $APP_STORE_CONNECT_API_KEY_ISSUER_ID
      APP_STORE_CONNECT_API_KEY_KEY: $APP_STORE_CONNECT_API_KEY_KEY
